<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> Po<PERSON>cier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\VarDumper\Dumper;

use Symfony\Component\VarDumper\Cloner\Data;
use Symfony\Component\VarDumper\Dumper\ContextProvider\ContextProviderInterface;

/**
 * <AUTHOR> <<EMAIL>>
 */
class ContextualizedDumper implements DataDumperInterface
{
    /**
     * @param ContextProviderInterface[] $contextProviders
     */
    public function __construct(
        private DataDumperInterface $wrappedDumper,
        private array $contextProviders,
    ) {
    }

    public function dump(Data $data): ?string
    {
        $context = $data->getContext();
        foreach ($this->contextProviders as $contextProvider) {
            $context[$contextProvider::class] = $contextProvider->getContext();
        }

        return $this->wrappedDumper->dump($data->withContext($context));
    }
}
