{"name": "ziggy-js", "version": "2.5.3", "description": "Use your Laravel named routes in JavaScript.", "keywords": ["laravel", "routes", "ziggy"], "homepage": "https://github.com/tighten/ziggy", "bugs": "https://github.com/tighten/ziggy/issues", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tighten/ziggy.git"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "files": ["src/js/index.d.ts", "dist"], "type": "module", "source": "./src/js/index.js", "exports": {"types": "./src/js/index.d.ts", "default": "./dist/index.js"}, "module": "./dist/index.esm.js", "types": "./src/js/index.d.ts", "scripts": {"build": "npm run build:esm && npm run build:umd", "build:esm": "microbundle -i ./src/js/index.js -o ./dist/index.js --format modern,esm --no-sourcemap --no-generateTypes --external none", "build:npm": "microbundle -i ./src/js/index.js -o ./dist/index.js --format modern,esm --no-sourcemap --no-generateTypes", "build:umd": "microbundle -i ./src/js/browser.js -o ./dist/route.js --format umd --name route --no-sourcemap --no-generateTypes --external none", "test": "vitest --typecheck", "format": "prettier . --write", "prepublishOnly": "rm -r ./dist/* && npm run build:npm"}, "mangle": {"regex": "^_(?!query)"}, "dependencies": {"@types/qs": "^6.9.17", "qs": "~6.9.7"}, "devDependencies": {"jsdom": "^26.1.0", "microbundle": "^0.15.1", "prettier": "^3.5.3", "typescript": "^5.8.3", "vitest": "^3.1.3"}, "prettier": {"printWidth": 100, "singleQuote": true, "tabWidth": 4}}