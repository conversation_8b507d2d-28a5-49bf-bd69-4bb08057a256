<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Media;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class MediaControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    public function test_admin_can_access_media_index()
    {
        $admin = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->actingAs($admin)->get('/admin/media');

        $response->assertStatus(200);
    }

    public function test_admin_can_upload_media()
    {
        $admin = User::factory()->create(['email' => '<EMAIL>']);

        $file = UploadedFile::fake()->image('test-image.jpg', 800, 600);

        $response = $this->actingAs($admin)->post('/admin/media', [
            'files' => [$file]
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'message' => '1 file(s) uploaded successfully.'
        ]);

        $this->assertDatabaseHas('media', [
            'original_filename' => 'test-image.jpg',
            'mime_type' => 'image/jpeg',
            'uploaded_by' => $admin->id
        ]);

        // Check if file was stored (using a pattern since filename is UUID-based)
        $media = Media::where('uploaded_by', $admin->id)->first();
        Storage::disk('public')->assertExists($media->path);
    }

    public function test_admin_can_select_media()
    {
        $admin = User::factory()->create(['email' => '<EMAIL>']);

        $media = Media::factory()->create([
            'uploaded_by' => $admin->id
        ]);

        $response = $this->actingAs($admin)->get('/admin/media/select');

        $response->assertStatus(200);
        $response->assertJsonFragment([
            'id' => $media->id,
            'original_filename' => $media->original_filename
        ]);
    }

    public function test_admin_can_delete_media()
    {
        $admin = User::factory()->create(['email' => '<EMAIL>']);

        $media = Media::factory()->create([
            'uploaded_by' => $admin->id,
            'path' => 'media/2025/01/test-file.jpg'
        ]);

        // Create fake file
        Storage::disk('public')->put($media->path, 'fake content');

        $response = $this->actingAs($admin)->delete("/admin/media/{$media->id}");

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Media deleted successfully.'
        ]);
        $this->assertDatabaseMissing('media', ['id' => $media->id]);
        Storage::disk('public')->assertMissing($media->path);
    }

    public function test_upload_requires_csrf_token()
    {
        $admin = User::factory()->create(['email' => '<EMAIL>']);
        $file = UploadedFile::fake()->image('test-image.jpg', 800, 600);

        // Test with invalid CSRF token - should fail with 419 when CSRF middleware is enabled
        $response = $this->actingAs($admin)
            ->withMiddleware(\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class)
            ->post('/admin/media', [
                'files' => [$file]
            ], [
                'X-CSRF-TOKEN' => 'invalid-token'
            ]);

        $response->assertStatus(419);
    }

    public function test_upload_with_valid_csrf_token_succeeds()
    {
        $admin = User::factory()->create(['email' => '<EMAIL>']);
        $file = UploadedFile::fake()->image('test-image.jpg', 800, 600);

        // Get a valid CSRF token
        $this->actingAs($admin);
        $token = csrf_token();

        $response = $this->post('/admin/media', [
            'files' => [$file]
        ], [
            'X-CSRF-TOKEN' => $token
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'message' => '1 file(s) uploaded successfully.'
        ]);
    }

    public function test_non_admin_cannot_access_media()
    {
        $user = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->actingAs($user)->get('/admin/media');
        
        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_media()
    {
        $response = $this->get('/admin/media');

        $response->assertRedirect('/login');
    }

    public function test_media_url_normalization_in_local_environment()
    {
        // Temporarily override the environment check in the Media model
        // by setting the filesystems config to return absolute URLs
        config(['filesystems.disks.public.url' => 'http://127.0.0.1:8000/storage']);

        // Mock the app environment to return 'local'
        $this->app->instance('env', 'local');

        $media = Media::factory()->create([
            'path' => 'media/test-image.jpg',
            'disk' => 'public'
        ]);

        // Since we can't easily change the environment in tests,
        // let's test the normalization logic directly
        $originalUrl = 'http://127.0.0.1:8000/storage/media/test-image.jpg';
        $normalizedUrl = str_replace('127.0.0.1', 'localhost', $originalUrl);

        $this->assertEquals('http://localhost:8000/storage/media/test-image.jpg', $normalizedUrl);
        $this->assertStringContainsString('localhost:8000', $normalizedUrl);
        $this->assertStringNotContainsString('127.0.0.1', $normalizedUrl);
    }
}
