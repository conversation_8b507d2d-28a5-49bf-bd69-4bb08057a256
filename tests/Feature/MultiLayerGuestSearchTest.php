<?php

namespace Tests\Feature;

use App\Models\SearchConfiguration;
use App\Services\MultiLayerGuestTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class MultiLayerGuestSearchTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Cache::flush();

        // Set test configuration
        SearchConfiguration::set('guest_search_limit', 3);
        SearchConfiguration::set('guest_search_reset_hours', 24);
        SearchConfiguration::set('track_guest_searches', true);

        // Disable middleware for testing
        $this->withoutMiddleware([\App\Http\Middleware\GuestSearchRateLimit::class]);
    }

    /**
     * Helper method to make guest search requests with proper headers
     */
    private function makeGuestSearchRequest(array $params = [])
    {
        return $this->withHeaders([
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query($params));
    }

    /**
     * Helper method to make guest search status requests with proper headers
     */
    private function makeGuestSearchStatusRequest(array $params = [])
    {
        return $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search/status?' . http_build_query($params));
    }

    public function test_guest_search_works_with_valid_device_id(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/guest-results')
                ->has('results')
                ->has('searches_used')
                ->has('remaining_searches')
        );
    }

    public function test_guest_search_with_fingerprint_data(): void
    {
        $deviceId = 'test_device_' . time();
        $fingerprint = hash('sha256', 'test_fingerprint_data');

        $response = $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId,
            'fingerprint' => $fingerprint
        ]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/guest-results')
                ->has('results')
                ->has('searches_used')
                ->has('remaining_searches')
        );
    }

    public function test_search_limit_enforcement_across_multiple_layers(): void
    {
        $deviceId = 'test_device_' . time();
        $fingerprint = hash('sha256', 'test_fingerprint');

        // Perform 3 searches (the limit)
        for ($i = 0; $i < 3; $i++) {
            $response = $this->get('/guest/search?' . http_build_query([
                'q' => "iPhone {$i}",
                'type' => 'all',
                'device_id' => $deviceId,
                'fingerprint' => $fingerprint
            ]));

            $response->assertStatus(200);
        }

        // 4th search should be blocked
        $response = $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone 4',
            'type' => 'all',
            'device_id' => $deviceId,
            'fingerprint' => $fingerprint
        ]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/guest-limit-exceeded')
                ->where('limit_reached', true)
                ->has('tracking_layers')
        );
    }

    public function test_ip_based_limit_enforcement(): void
    {
        // Use different device IDs but same IP
        $deviceId1 = 'device_1_' . time();
        $deviceId2 = 'device_2_' . time();

        // Perform searches with first device
        for ($i = 0; $i < 3; $i++) {
            $response = $this->get('/guest/search?' . http_build_query([
                'q' => "iPhone {$i}",
                'type' => 'all',
                'device_id' => $deviceId1
            ]));

            $response->assertStatus(200);
        }

        // Try with different device ID from same IP - should be blocked
        $response = $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone test',
            'type' => 'all',
            'device_id' => $deviceId2
        ]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/guest-limit-exceeded')
                ->where('limit_reached', true)
        );
    }

    public function test_session_based_tracking(): void
    {
        $deviceId = 'test_device_' . time();

        // Start a session and perform searches
        $this->withSession(['test' => 'value']);

        for ($i = 0; $i < 2; $i++) {
            $response = $this->get('/guest/search?' . http_build_query([
                'q' => "iPhone {$i}",
                'type' => 'all',
                'device_id' => $deviceId
            ]));

            $response->assertStatus(200);
        }

        // Check that session tracking is working
        $this->assertSessionHas('guest_search_count');
        $this->assertEquals(2, session('guest_search_count'));
    }

    public function test_search_status_endpoint(): void
    {
        $deviceId = 'test_device_' . time();

        // Initial status
        $response = $this->get('/guest/search/status?' . http_build_query([
            'device_id' => $deviceId
        ]));

        $response->assertStatus(200);
        $response->assertJson([
            'can_search' => true,
            'searches_used' => 0,
            'search_limit' => 3,
            'remaining_searches' => 3,
        ]);

        // After one search
        $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $response = $this->get('/guest/search/status?' . http_build_query([
            'device_id' => $deviceId
        ]));

        $response->assertStatus(200);
        $response->assertJson([
            'can_search' => true,
            'searches_used' => 1,
            'remaining_searches' => 2,
        ]);
    }

    public function test_suspicious_activity_detection(): void
    {
        $deviceId = 'test_device_' . time();

        // Simulate rapid searches
        for ($i = 0; $i < 5; $i++) {
            $this->get('/guest/search?' . http_build_query([
                'q' => "rapid_search_{$i}",
                'type' => 'all',
                'device_id' => $deviceId
            ]));
        }

        // Check status for suspicious activity
        $response = $this->get('/guest/search/status?' . http_build_query([
            'device_id' => $deviceId
        ]));

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertArrayHasKey('suspicious_activity', $data);
    }

    public function test_fingerprint_validation(): void
    {
        $deviceId = 'test_device_' . time();
        $invalidFingerprint = 'invalid_fingerprint';

        $response = $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId,
            'fingerprint' => $invalidFingerprint
        ]));

        // Should still work but ignore invalid fingerprint
        $response->assertStatus(200);
    }

    public function test_bypass_attempt_detection(): void
    {
        $baseDeviceId = 'device_' . time();

        // Try to bypass by changing device ID
        for ($i = 0; $i < 5; $i++) {
            $deviceId = $baseDeviceId . '_' . $i;
            
            $response = $this->get('/guest/search?' . http_build_query([
                'q' => "bypass_attempt_{$i}",
                'type' => 'all',
                'device_id' => $deviceId
            ]));

            if ($i >= 3) {
                // Should be blocked due to IP-based tracking
                $response->assertStatus(200);
                $response->assertInertia(fn ($page) =>
                    $page->component('search/guest-limit-exceeded')
                );
                break;
            }
        }
    }

    public function test_api_response_format(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->getJson('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'results',
            'query',
            'search_type',
            'total',
            'searches_used',
            'search_limit',
            'remaining_searches',
            'message',
        ]);
    }

    public function test_limit_exceeded_api_response(): void
    {
        $deviceId = 'test_device_' . time();

        // Exhaust search limit
        for ($i = 0; $i < 3; $i++) {
            $this->getJson('/guest/search?' . http_build_query([
                'q' => "iPhone {$i}",
                'type' => 'all',
                'device_id' => $deviceId
            ]));
        }

        // Next request should return 429
        $response = $this->getJson('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $response->assertStatus(429);
        $response->assertJsonStructure([
            'error',
            'message',
            'limit_reached',
            'signup_url',
            'login_url',
        ]);
    }

    public function test_missing_device_id_validation(): void
    {
        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
        ]));

        $response->assertStatus(422);
    }

    public function test_invalid_search_query_validation(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->get('/guest/search?' . http_build_query([
            'q' => 'a', // Too short
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $response->assertStatus(422);
    }

    public function test_search_analytics_tracking(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $response->assertStatus(200);

        // Check that analytics data is stored
        $today = now()->format('Y-m-d');
        $dailyCount = Cache::get("guest_searches_count_{$today}", 0);
        $this->assertGreaterThan(0, $dailyCount);
    }
}
