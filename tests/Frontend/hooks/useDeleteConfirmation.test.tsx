import { renderHook, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';

// Mock createRoot
const mockRender = vi.fn();
const mockUnmount = vi.fn();

vi.mock('react-dom/client', () => ({
    createRoot: vi.fn(() => ({
        render: mockRender,
        unmount: mockUnmount,
    })),
}));

// Mock document methods
const mockAppendChild = vi.fn();
const mockRemoveChild = vi.fn();
const mockCreateElement = vi.fn(() => ({
    style: {},
}));

Object.defineProperty(document, 'body', {
    value: {
        appendChild: mockAppendChild,
        removeChild: mockRemoveChild,
        style: {
            removeProperty: vi.fn(),
            setProperty: vi.fn()
        },
    },
    writable: true,
});

Object.defineProperty(document, 'createElement', {
    value: mockCreateElement,
    writable: true,
});

describe('useDeleteConfirmation Hook', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        // Clean up any event listeners
        document.removeEventListener('keydown', vi.fn());
    });

    it('returns showDeleteConfirmation and confirmDelete methods', () => {
        const { result } = renderHook(() => useDeleteConfirmation());
        
        expect(result.current).toHaveProperty('showDeleteConfirmation');
        expect(result.current).toHaveProperty('confirmDelete');
        expect(typeof result.current.showDeleteConfirmation).toBe('function');
        expect(typeof result.current.confirmDelete).toBe('function');
    });

    it('confirmDelete is an alias for showDeleteConfirmation', () => {
        const { result } = renderHook(() => useDeleteConfirmation());
        
        expect(result.current.confirmDelete).toBe(result.current.showDeleteConfirmation);
    });

    it('creates modal container when showDeleteConfirmation is called', () => {
        const { result } = renderHook(() => useDeleteConfirmation());
        
        const mockOptions = {
            title: 'Delete Item',
            description: 'Are you sure you want to delete this item?',
            onConfirm: vi.fn(),
            onCancel: vi.fn(),
        };

        act(() => {
            result.current.showDeleteConfirmation(mockOptions);
        });

        expect(mockCreateElement).toHaveBeenCalledWith('div');
        expect(mockAppendChild).toHaveBeenCalled();
        expect(mockRender).toHaveBeenCalled();
    });

    it('creates modal container when confirmDelete is called', () => {
        const { result } = renderHook(() => useDeleteConfirmation());
        
        const mockOptions = {
            title: 'Delete Item',
            description: 'Are you sure you want to delete this item?',
            onConfirm: vi.fn(),
            onCancel: vi.fn(),
        };

        act(() => {
            result.current.confirmDelete(mockOptions);
        });

        expect(mockCreateElement).toHaveBeenCalledWith('div');
        expect(mockAppendChild).toHaveBeenCalled();
        expect(mockRender).toHaveBeenCalled();
    });

    it('passes correct props to modal component', () => {
        const { result } = renderHook(() => useDeleteConfirmation());
        
        const mockOptions = {
            title: 'Delete Item',
            description: 'Are you sure you want to delete this item?',
            onConfirm: vi.fn(),
            onCancel: vi.fn(),
            confirmText: 'Delete',
            cancelText: 'Cancel',
        };

        act(() => {
            result.current.showDeleteConfirmation(mockOptions);
        });

        expect(mockRender).toHaveBeenCalledWith(
            expect.objectContaining({
                props: expect.objectContaining({
                    title: 'Delete Item',
                    description: 'Are you sure you want to delete this item?',
                    onConfirm: expect.any(Function),
                    onCancel: expect.any(Function),
                    confirmText: 'Delete',
                    cancelText: 'Cancel',
                    onClose: expect.any(Function),
                }),
            })
        );
    });

    it('uses default text when confirmText and cancelText are not provided', () => {
        const { result } = renderHook(() => useDeleteConfirmation());
        
        const mockOptions = {
            title: 'Delete Item',
            onConfirm: vi.fn(),
        };

        act(() => {
            result.current.showDeleteConfirmation(mockOptions);
        });

        expect(mockRender).toHaveBeenCalledWith(
            expect.objectContaining({
                props: expect.objectContaining({
                    confirmText: 'Delete',
                    cancelText: 'Cancel',
                }),
            })
        );
    });

    it('handles multiple confirmation dialogs', () => {
        const { result } = renderHook(() => useDeleteConfirmation());
        
        const mockOptions1 = {
            title: 'Delete Item 1',
            onConfirm: vi.fn(),
        };

        const mockOptions2 = {
            title: 'Delete Item 2',
            onConfirm: vi.fn(),
        };

        act(() => {
            result.current.showDeleteConfirmation(mockOptions1);
            result.current.showDeleteConfirmation(mockOptions2);
        });

        expect(mockCreateElement).toHaveBeenCalledTimes(2);
        expect(mockAppendChild).toHaveBeenCalledTimes(2);
        expect(mockRender).toHaveBeenCalledTimes(2);
    });

    it('provides onClose callback that cleans up modal', () => {
        const { result } = renderHook(() => useDeleteConfirmation());
        
        const mockOptions = {
            title: 'Delete Item',
            onConfirm: vi.fn(),
        };

        act(() => {
            result.current.showDeleteConfirmation(mockOptions);
        });

        // Get the onClose callback from the render call
        const renderCall = mockRender.mock.calls[0][0];
        const onClose = renderCall.props.onClose;

        expect(typeof onClose).toBe('function');

        // Call onClose to simulate modal cleanup
        act(() => {
            onClose();
        });

        expect(mockUnmount).toHaveBeenCalled();
        expect(mockRemoveChild).toHaveBeenCalled();
    });

    it('handles missing optional callbacks gracefully', () => {
        const { result } = renderHook(() => useDeleteConfirmation());
        
        const mockOptions = {
            title: 'Delete Item',
            onConfirm: vi.fn(),
            // onCancel is optional and not provided
        };

        expect(() => {
            act(() => {
                result.current.showDeleteConfirmation(mockOptions);
            });
        }).not.toThrow();

        expect(mockRender).toHaveBeenCalledWith(
            expect.objectContaining({
                props: expect.objectContaining({
                    title: 'Delete Item',
                    onConfirm: expect.any(Function),
                    onCancel: undefined,
                }),
            })
        );
    });
});
