import React from 'react';
import { render, screen, within } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { router } from '@inertiajs/react';
import UserShow from '../../resources/js/pages/admin/Users/<USER>';
import type { UserSearch } from '../../resources/js/types';

// Mock Inertia router
vi.mock('@inertiajs/react', async () => {
    const actual = await vi.importActual('@inertiajs/react');
    return {
        ...actual,
        router: {
            get: vi.fn(),
            post: vi.fn(),
            put: vi.fn(),
            delete: vi.fn(),
            reload: vi.fn(),
        },
        Head: ({ children }: { children: React.ReactNode }) => <>{children}</>,
        Link: ({ children, href }: { children: React.ReactNode; href: string }) => (
            <a href={href}>{children}</a>
        ),
    };
});

// Mock route helper
vi.mock('ziggy-js', () => ({
    route: vi.fn((name: string, params?: any) => {
        if (name === 'admin.users.index') return '/admin/users';
        if (name === 'admin.users.edit' && params) return `/admin/users/${params}/edit`;
        return `/${name}`;
    }),
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
    ArrowLeft: () => <div data-testid="arrow-left-icon" />,
    User: () => <div data-testid="user-icon" />,
    Mail: () => <div data-testid="mail-icon" />,
    Calendar: () => <div data-testid="calendar-icon" />,
    Activity: () => <div data-testid="activity-icon" />,
    CreditCard: () => <div data-testid="credit-card-icon" />,
    Eye: () => <div data-testid="eye-icon" />,
    Ban: () => <div data-testid="ban-icon" />,
    CheckCircle: () => <div data-testid="check-circle-icon" />,
    DollarSign: () => <div data-testid="dollar-sign-icon" />,
    Search: () => <div data-testid="search-icon" />,
    Heart: () => <div data-testid="heart-icon" />,
    Edit: () => <div data-testid="edit-icon" />,
    Save: () => <div data-testid="save-icon" />,
    X: () => <div data-testid="x-icon" />,
    XIcon: () => <div data-testid="x-icon" />,
    Key: () => <div data-testid="key-icon" />,
    EyeOff: () => <div data-testid="eye-off-icon" />,
}));

// Mock AppLayout
vi.mock('../../resources/js/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

// Mock ImpersonationSecurityCheck
vi.mock('../../resources/js/components/ImpersonationSecurityCheck', () => ({
    default: () => <div data-testid="impersonation-security-check" />,
}));

// Mock UI components
vi.mock('@/components/ui/tabs', () => ({
    Tabs: ({ children, defaultValue, ...props }: any) => <div data-testid="tabs" data-default-value={defaultValue} {...props}>{children}</div>,
    TabsList: ({ children, ...props }: any) => <div data-testid="tabs-list" {...props}>{children}</div>,
    TabsTrigger: ({ children, value, ...props }: any) => (
        <button role="tab" data-testid={`tab-${value}`} data-value={value} {...props}>
            {children}
        </button>
    ),
    TabsContent: ({ children, value, ...props }: any) => <div data-testid={`tab-content-${value}`} data-value={value} {...props}>{children}</div>,
}));

describe('AdminUserSearchDisplay', () => {
    const mockUser = {
        id: 1,
        name: 'Test User',
        email: '<EMAIL>',
        status: 'active' as const,
        approval_status: 'approved' as const,
        subscription_plan: 'free' as const,
        search_count: 5,
        login_count: 10,
        created_at: '2024-01-01T00:00:00.000000Z',
        last_login_at: '2024-01-15T10:30:00.000000Z',
        approved_at: '2024-01-01T12:00:00.000000Z',
        suspended_at: null,
        suspension_reason: null,
        suspension_expires_at: null,
        searches_count: 3,
        payment_requests_count: 0,
        activity_logs_count: 5,
        favorites_count: 2,
        subscriptions: [],
        payment_requests: [],
        activity_logs: [],
        searches: [] as UserSearch[],
        favorites: [],
    };

    it('displays user searches correctly with proper field names', () => {
        const userWithSearches = {
            ...mockUser,
            searches: [
                {
                    id: 1,
                    user_id: 1,
                    search_query: 'iPhone 12 battery',
                    search_type: 'part_name',
                    results_count: 15,
                    created_at: '2024-01-15T10:30:00.000000Z',
                },
                {
                    id: 2,
                    user_id: 1,
                    search_query: 'Samsung Galaxy S21',
                    search_type: 'model',
                    results_count: 8,
                    created_at: '2024-01-14T14:20:00.000000Z',
                },
            ] as UserSearch[],
        };

        render(<UserShow user={userWithSearches} />);

        // Click on the searches tab
        const searchesTab = screen.getByRole('tab', { name: /searches/i });
        searchesTab.click();

        // Check that search queries are displayed correctly
        expect(screen.getByText('"iPhone 12 battery"')).toBeInTheDocument();
        expect(screen.getByText('"Samsung Galaxy S21"')).toBeInTheDocument();

        // Verify the search count is displayed
        expect(screen.getByText('Latest 2 searches')).toBeInTheDocument();
    });

    it('displays empty state when user has no searches', () => {
        render(<UserShow user={mockUser} />);

        // Click on the searches tab
        const searchesTab = screen.getByRole('tab', { name: /searches/i });
        searchesTab.click();

        // Check for empty state message
        expect(screen.getByText('No searches found')).toBeInTheDocument();
        expect(screen.getByText('Latest 0 searches')).toBeInTheDocument();
    });

    it('handles special characters in search queries correctly', () => {
        const userWithSpecialSearches = {
            ...mockUser,
            searches: [
                {
                    id: 1,
                    user_id: 1,
                    search_query: 'iPhone "Pro Max" & accessories',
                    search_type: 'all',
                    results_count: 12,
                    created_at: '2024-01-15T10:30:00.000000Z',
                },
                {
                    id: 2,
                    user_id: 1,
                    search_query: 'Samsung <Galaxy> S21+',
                    search_type: 'model',
                    results_count: 7,
                    created_at: '2024-01-14T14:20:00.000000Z',
                },
            ] as UserSearch[],
        };

        render(<UserShow user={userWithSpecialSearches} />);

        // Click on the searches tab
        const searchesTab = screen.getByRole('tab', { name: /searches/i });
        searchesTab.click();

        // Check that special characters are displayed correctly
        expect(screen.getByText('"iPhone "Pro Max" & accessories"')).toBeInTheDocument();
        expect(screen.getByText('"Samsung <Galaxy> S21+"')).toBeInTheDocument();
    });

    it('handles empty search queries correctly', () => {
        const userWithEmptySearch = {
            ...mockUser,
            searches: [
                {
                    id: 1,
                    user_id: 1,
                    search_query: '',
                    search_type: 'all',
                    results_count: 0,
                    created_at: '2024-01-15T10:30:00.000000Z',
                },
            ] as UserSearch[],
        };

        render(<UserShow user={userWithEmptySearch} />);

        // Click on the searches tab
        const searchesTab = screen.getByRole('tab', { name: /searches/i });
        searchesTab.click();

        // Check that empty search query is displayed as empty quotes
        expect(screen.getByText('""')).toBeInTheDocument();
    });

    it('displays search timestamps correctly', () => {
        const userWithSearches = {
            ...mockUser,
            searches: [
                {
                    id: 1,
                    user_id: 1,
                    search_query: 'Test Query',
                    search_type: 'part_name',
                    results_count: 5,
                    created_at: '2024-01-15T10:30:00.000000Z',
                },
            ] as UserSearch[],
        };

        render(<UserShow user={userWithSearches} />);

        // Click on the searches tab
        const searchesTab = screen.getByRole('tab', { name: /searches/i });
        searchesTab.click();

        // Check that the timestamp is displayed (format may vary based on locale)
        const searchContainer = screen.getByText('"Test Query"').closest('div');
        expect(searchContainer).toBeInTheDocument();
        
        // The exact format depends on the browser's locale, but it should contain date/time info
        const timestampElement = within(searchContainer!).getByText(/2024/);
        expect(timestampElement).toBeInTheDocument();
    });

    it('displays search icons correctly', () => {
        const userWithSearches = {
            ...mockUser,
            searches: [
                {
                    id: 1,
                    user_id: 1,
                    search_query: 'Test Query',
                    search_type: 'part_name',
                    results_count: 5,
                    created_at: '2024-01-15T10:30:00.000000Z',
                },
            ] as UserSearch[],
        };

        render(<UserShow user={userWithSearches} />);

        // Click on the searches tab
        const searchesTab = screen.getByRole('tab', { name: /searches/i });
        searchesTab.click();

        // Check that search icons are displayed
        expect(screen.getByTestId('search-icon')).toBeInTheDocument();
    });
});
